﻿   Qt/MSBuild: *******
  Reading Qt configuration (D:/QT/5.14.2/msvc2017_64/bin/qmake)
   Qt: 5.14.2
  uic MyMainWindow.ui
  moc include\MyMainWindow.h
  main.cpp
  MyMainWindow.cpp
  RtspSoftwareFactory.cpp
  MediaPipeline.cpp
  MediaPlayerFacade.cpp
  MyPlayer.cpp
  OpenGLConsumer.cpp
  RTSP.cpp
D:\MyPlayer\MyPlayer\src\MediaPipeline.cpp(35): warning C4715: “MediaPipelineImpl::start”: 不是所有的控件路径都返回值
  SoftDecoder.cpp
D:\MyPlayer\3rdparty\ffmpeg-4.3.1\build\windows\x64\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\factories\RtspSoftwareFactory.cpp)
D:\MyPlayer\MyPlayer\src\products\RTSP.cpp(170,60): warning C4267: “参数”: 从“size_t”转换到“int”，可能丢失数据
D:\MyPlayer\MyPlayer\src\products\RTSP.cpp(228,43): warning C4244: “参数”: 从“SOCKET”转换到“int”，可能丢失数据
D:\MyPlayer\MyPlayer\src\products\RTSP.cpp(346,72): warning C4267: “初始化”: 从“size_t”转换到“uint32_t”，可能丢失数据
D:\MyPlayer\MyPlayer\src\products\RTSP.cpp(468,32): warning C4244: “参数”: 从“SOCKET”转换到“int”，可能丢失数据
D:\MyPlayer\3rdparty\ffmpeg-4.3.1\build\windows\x64\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\MediaPlayerFacade.cpp)
D:\MyPlayer\3rdparty\ffmpeg-4.3.1\build\windows\x64\include\libavutil\rational.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失 (编译源文件 src\products\SoftDecoder.cpp)
D:\MyPlayer\MyPlayer\src\products\SoftDecoder.cpp(53,55): warning C4267: “=”: 从“size_t”转换到“int”，可能丢失数据
  moc_MyMainWindow.cpp
  MyPlayer.vcxproj -> D:\MyPlayer\x64\Debug\MyPlayer.exe
