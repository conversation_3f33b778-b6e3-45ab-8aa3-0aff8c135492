#pragma once
#include"../include/MediaPlayer.h"
#include"internal/Share.h"
#include <functional>
#include <string>
#include <vector>
#include <map>
#include <mutex>

class EventDispatcher {
public:
	using Callback = std::function<void(const DecodedFrame&)>;
	//����һ������
	void subscribe(const std::string& topic, Callback callback) {
		std::lock_guard<std::mutex> lock(m_mutex);
		m_subscribers[topic].push_back(std::move(callback));
	}
	//����һ������
	void publish(const std::string& topic, const DecodedFrame& frame) {
		std::lock_guard<std::mutex> lock(m_mutex);
		if (m_subscribers.count(topic)) {
			for (const auto& callback : m_subscribers.at(topic)) {
				callback(frame);
			}
		}
	}
private:
	std::map<std::string, std::vector<Callback>> m_subscribers;
	std::mutex m_mutex;
};