#include "RTSP.h"

#define DEFAULT_PORT    554
RTSPClient::RTSPClient(const Properties pProperties) 
    : m_Properties(pProperties),
      m_bStreamOverTCP(false),
      m_expectedPacketSize(0),
      m_iIdentity(),
      m_iSocketFd(-1),
      m_iUserChannelId(-1),
      m_pUserPtr(nullptr),
      m_framecallback(nullptr),
      m_parserState(ParserState::Synchronizing),
      m_running(false)
{
}
RTSPClient::~RTSPClient(){
    CloseStream();
}
template<typename U>
bool RTSPClient::try_get_property(const std::string& key, U& out_value) {
    auto it = m_Properties.find(key);
    if (it == m_Properties.end()) {
        return false;
    }
    const std::any& value = it->second;

    const U* p_value = std::any_cast<U>(&value);
    if (!p_value) {
        return false;
    }
    out_value = *p_value;
    return true;
}

template<typename T>
void RTSPClient::get_property_optional(const std::string& key, T& out_value, const T& default_value) {
    auto it = m_Properties.find(key);
    if (it == m_Properties.end()) {
        out_value = default_value;
        return;
    }
    const T* p_value = std::any_cast<T>(&it->second);
    if (p_value) {
        out_value = *p_value;
    }
    else {
        out_value = default_value;
        std::cerr << "配置警告: 键 '" << key << "' 的类型不正确，已使用默认值 :" << default_value << std::endl;
    }
}

bool RTSPClient::initialize(){
    if (!try_get_property("url", m_strUrl) || !try_get_property("user_ptr", m_pUserPtr)|| 
        !try_get_property("rtspsourcecallback", m_framecallback)) {
        return false;
    }
    get_property_optional("stream_over_tcp", m_bStreamOverTCP, false); // 默认为 false
    get_property_optional("identity", m_iIdentity, 0); // 默认为 0
    get_property_optional("user_channel_id", m_iUserChannelId, -1); // 默认为 0
    return true;
}

//void RTSPClient::SetCallback(STRTSPSourceCallBack cb) {
//    m_rtspsourcecallback = cb;
//}

void RTSPClient::SetCallback(FrameCallback cb) {
    m_framecallback = cb;
}

static std::string getIP(const std::string& _url)
{
    std::regex ip_regex("rtsp://(\\d{1,3}(\\.\\d{1,3}){3})(:\\d+)?\\S*");
    std::smatch results;
    try
    {
        if (std::regex_match(_url, results, ip_regex))
        {
            return results[1].str();
        }
        else return std::string();
    }
    catch (std::regex_error e)
    {
        std::cerr << e.what() << '\t' << e.code() << std::endl;
        return std::string();
    }
}

static int getPort(const std::string& _url)
{
    std::regex port_regex("rtsp://(\\d{1,3}(\\.\\d{1,3}){3})(:(\\d+))?\\S*");
    std::smatch results;

    try
    {
        if (std::regex_match(_url, results, port_regex))
        {
            if (results[4].matched)
            {
                return std::stoi(results[4].str());
            }
            else return DEFAULT_PORT;
        }
        else return -1;
    }
    catch (std::regex_error e)
    {
        std::cerr << e.what() << '\t' << e.code() << std::endl;
        return -1;
    }
    catch (std::exception e)
    {
        std::cerr << e.what() << std::endl;
        return -1;
    }
}

bool RTSPClient::updateProperties(Properties* _properties){
    if(_properties->find("url") != _properties->end()){
        m_strUrl = std::any_cast<std::string>(_properties->at("url"));
    }
    if(_properties->find("stream_over_tcp") != _properties->end()){
        m_bStreamOverTCP = std::any_cast<bool>(_properties->at("stream_over_tcp"));
    }
    if(_properties->find("user_ptr") != _properties->end()){
        m_pUserPtr = std::any_cast<void*>(_properties->at("user_ptr"));
    }
    if(_properties->find("user_channel_id") != _properties->end()){
        m_iUserChannelId = std::any_cast<int>(_properties->at("user_channel_id"));
    }
    if(_properties->find("identity") != _properties->end()){
        m_iIdentity = std::any_cast<int>(_properties->at("identity"));
    }
    return true;
}

int RTSPClient::getIdentity(){
    return  m_iIdentity;
}

int RTSPClient::sendCommand(const void *command,const int lenth){
    if (m_iSocketFd < 0)
    return ST_InvalidSocket;
    static int number =0;
    TCP_REQ_S reqPacket;
    memcpy(reqPacket.sCmdFlag,command, lenth);
    reqPacket.cType = 1;
    reqPacket.cFlag = number++;
    if(number == 255) number = 0;
    auto now  = std::chrono::high_resolution_clock::now();
    auto timestamp = std::chrono::duration_cast<std::chrono::microseconds>(
        now.time_since_epoch()).count();
    reqPacket.u64Pts = static_cast<uint64_t>(timestamp);

#ifdef DEBUGINFO
    std::cout << "SEND START MESSAGE: " << reqPacket.sCmdFlag 
              << ", Type: " << (int)reqPacket.cType 
              << ", Flag: " << (int)reqPacket.cFlag 
              << ", Timestamp: " << reqPacket.u64Pts << std::endl;
#endif
    const char* msg = reinterpret_cast<const char *>(&reqPacket);
    size_t size = sizeof(TCP_REQ_S);
    int sendResult = 0;
    int index = 0;
    int err = ST_NoErr;
    while (size > 0)
    {
        sendResult = send(m_iSocketFd, msg + index, size, 0);
        if (sendResult < 0)
        {
            if (errno == EINTR) continue;
            else if (errno == EWOULDBLOCK || errno == EAGAIN) continue;
            else {
                err = ST_SendErr;
                break;
            }
        }
        else if (sendResult == 0)
        {
            err = ST_SendErr;
            break;
        }

        index += sendResult;
        size -= sendResult;
    }
    return err;
}

int RTSPClient::OpenStream(){
    ST_Error ret = ST_NoErr;
    ret = createSocket();
    if (ret != ST_NoErr)
    {
        return ret;
    }
    ret = connectToServer();
    if (ret != ST_NoErr)
    {
        return ret;
    }
    ret = sendCommand("#STA",4);
    if(ret != ST_NoErr){
        return ret;
    }
    m_running = true;
    m_reciverThread = std::thread(&RTSPClient::receiverLoop,this);
    m_parserThread = std::thread(&RTSPClient::parserLoop,this);
    keepAliverThread = std::thread(&RTSPClient::keepAliverLoop,this);
    if(m_framecallback != nullptr){
        m_framecallback(m_iUserChannelId, m_pUserPtr,ST_SDK_MEDIA_INFO_FLAG,nullptr,nullptr);
    }
    return ST_NoErr;
}

void RTSPClient::receiverLoop(){
    while(m_running){
        char recvBuff[4096];
        fd_set read_fds;
        FD_ZERO(&read_fds);
        FD_SET(m_iSocketFd, &read_fds);

        struct timeval tv;
        tv.tv_sec = 0;
        tv.tv_usec = 20000;
        int activate = select(m_iSocketFd + 1,&read_fds,NULL,NULL,&tv);
        if((activate<0) && (errno != EINTR)){
            perror("select error");
            break;
        }
        if(activate == 0){
            continue;
        }
        if(FD_ISSET(m_iSocketFd,&read_fds)){
            int recvlen =recv(m_iSocketFd,recvBuff,sizeof(recvBuff),0);
            if(recvlen > 0){
                {
                    std::lock_guard<std::mutex> lock(m_receiveBuffmutex);
                    m_receiveBuffer.insert(m_receiveBuffer.end(),recvBuff,recvBuff+recvlen);  
                }
                m_receiveBuffCond.notify_one();
            }else if(recvlen == 0){
                break;
            }else{
                if (errno == EWOULDBLOCK || errno == EAGAIN) {
                    continue; 
                } else {
                    perror("recv error");
                    break;
                }
            }
        }
    }
}

void RTSPClient::keepAliverLoop(){
    while(m_running){
        sendCommand("#ALV",4);
        std::unique_lock<std::mutex> lock(m_keepAliveMutex);
        m_keepAliveCondition.wait_for(lock, std::chrono::seconds(2));
    }
}

void RTSPClient::parserLoop(){
    const int HEADER_SIZE = 18;
    std::string  STREAM_FLAG ="#WFC";
    size_t HEADER_FLAG_LEN = 4;
    char* headerBuffer = new char[HEADER_SIZE]; 
    NaluHeader naluheader = {0};
    std::vector<unsigned char> start_code={0x00,0x00,0x00,0x01};
    static int number = 0;

    while(m_running){
            std::unique_lock<std::mutex> lock(m_receiveBuffmutex);
            m_receiveBuffCond.wait(lock, [this] { return !m_receiveBuffer.empty() || !m_running; });
            
            if (!m_running && m_receiveBuffer.empty()) break;
        while (m_running && !m_receiveBuffer.empty()) {
            if (m_parserState == ParserState::Synchronizing) {
                auto it = std::search(m_receiveBuffer.begin(), m_receiveBuffer.end(),STREAM_FLAG.begin(), STREAM_FLAG.end());
                if (it == m_receiveBuffer.end()) {
                // 未找到包头，保留最后几个字节以防包头被分割
                size_t keep_size = (std::min)(static_cast<size_t>(HEADER_FLAG_LEN - 1), m_receiveBuffer.size());
                if (m_receiveBuffer.size() > keep_size) {
                m_receiveBuffer.erase(m_receiveBuffer.begin(), m_receiveBuffer.end() - keep_size);
                }
                // 缓冲区已处理完，跳出内层循环等待新数据
                break; 
                }
                m_receiveBuffer.erase(m_receiveBuffer.begin(), it);

                if (m_receiveBuffer.size() < HEADER_SIZE) { 
                    break;
                }
                uint32_t body_size;
                unsigned char body_size_bytes[sizeof(uint32_t)];
                std::copy(m_receiveBuffer.begin() + 14, m_receiveBuffer.begin() + 14 + sizeof(uint32_t), body_size_bytes);
                std::memcpy(&body_size, body_size_bytes, sizeof(uint32_t));
                // std::memcpy(&body_size, m_receiveBuffer.data() + 14, sizeof(uint32_t));
                // body_size = ntohl(body_size); // 字节序转换

                // 4. 验证包体大小
                if (body_size > 5 * 1024 * 1024) { // 包体过大
                    m_receiveBuffer.erase(m_receiveBuffer.begin());
                    continue;
                }

                // 5. 记录期望的包总长，并切换状态
                m_expectedPacketSize = HEADER_SIZE + body_size;
                m_parserState = ParserState::ReadingBody;
            }else if (m_parserState == ParserState::ReadingHead) {
                // Timeshow::getInstance().startRenderTime();
                if (m_receiveBuffer.size() < HEADER_SIZE) { 
                    break;
                }
                uint32_t body_size;
                unsigned char body_size_bytes[sizeof(uint32_t)];
                std::copy(m_receiveBuffer.begin() + 14, m_receiveBuffer.begin() + 14 + sizeof(uint32_t), body_size_bytes);
                std::memcpy(&body_size, body_size_bytes, sizeof(uint32_t));
                // std::memcpy(&body_size, m_receiveBuffer.data() + 14, sizeof(uint32_t));
                // body_size = ntohl(body_size); // 字节序转换

                // 4. 验证包体大小
                if (body_size > 5 * 1024 * 1024) { // 包体过大
                    m_receiveBuffer.erase(m_receiveBuffer.begin());
                    continue;
                }

                // 5. 记录期望的包总长，并切换状态
                m_expectedPacketSize = HEADER_SIZE + body_size;
                m_parserState = ParserState::ReadingBody;
            }else if (m_parserState == ParserState::ReadingBody) {
                if (m_receiveBuffer.size() < m_expectedPacketSize) {
                    break;
                }
                static int  total_number = 0;
                ParsedPacket outPacket;
                outPacket.packetType = m_receiveBuffer[4];
                outPacket.frameFlag = m_receiveBuffer[5];
                unsigned char timestamp_bytes[sizeof(uint64_t)];
                std::copy(m_receiveBuffer.begin() + 6, m_receiveBuffer.begin() + 6 + sizeof(uint64_t), timestamp_bytes);
                outPacket.timestamp_us = ntohll_custom(*(uint64_t*)timestamp_bytes);
                // outPacket.timestamp_us = ntohll_custom(*(uint64_t*)(m_receiveBuffer.data() + 6));
                uint32_t body_size = m_expectedPacketSize - HEADER_SIZE;
                outPacket.bodySize = body_size;
                outPacket.number = total_number++;
                outPacket.m_frameTime = std::chrono::high_resolution_clock::now();
                if (body_size > 0) {
                    outPacket.bodyData = new unsigned char[body_size];
                    std::copy(m_receiveBuffer.begin() + HEADER_SIZE, m_receiveBuffer.begin() + HEADER_SIZE + body_size, outPacket.bodyData);
                    // std::memcpy(outPacket.bodyData, m_receiveBuffer.data() + HEADER_SIZE, body_size);
                }
                {
                    std::lock_guard<std::mutex> queueLock(m_packetQueueMutex);
                    if(m_framecallback != nullptr){
                        m_framecallback(m_iUserChannelId, m_pUserPtr,ST_SDK_VIDEO_PRIVATE_FRAME_FLAG,(ST_FRAME*)&outPacket,nullptr);
                   }
                }
                m_packetQueueCond.notify_one();
                m_receiveBuffer.erase(m_receiveBuffer.begin(), m_receiveBuffer.begin() + m_expectedPacketSize);
                m_parserState = ParserState::ReadingHead;
                m_expectedPacketSize = 0;
                continue;
            }
        }
    }

}

int RTSPClient::CloseStream()
{
    ST_Error ret = ST_NoErr;
    sendCommand("#STO",4);
    if(m_running){
        m_running = false;
    }   
    if(m_iSocketFd){
        closeSocket();        
    }
    m_keepAliveCondition.notify_all();  
    m_receiveBuffCond.notify_all();
    if(keepAliverThread.joinable()){
        keepAliverThread.join();
    }
    if(m_reciverThread.joinable()){
        m_reciverThread.join();
    }
    if(m_parserThread.joinable()){
        m_parserThread.join();
    }
    return ret;
}

int RTSPClient::createSocket()
{
    // 创建socket
    static bool bHasWSAInit = false;
    if (!bHasWSAInit)
    {
#ifdef _WIN32
        WSADATA wsa;
        if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
            std::cerr << "Failed to initialize Winsock." << std::endl;
            return ST_InvalidSocket;
        }
#endif
    }
    SockType sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock < 0) {
        std::cerr << "Failed to create socket." << std::endl;
        return ST_InvalidSocket;
    }

#ifdef _WIN32
    int timeout = 5000; // 设置超时时间
    setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, (const char*)&timeout, sizeof timeout);
    setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, (const char*)&timeout, sizeof timeout);
#else
    struct timeval timeout = {5,0};
    setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof timeout);
    setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof timeout);
#endif
    
    m_iSocketFd = sock;
    return ST_NoErr;
}

int RTSPClient::connectToServer()
{
    int port = getPort(m_strUrl);
    if (port < 0) return ST_InvalidURL;

    std::string ip = getIP(m_strUrl);
    if(ip.empty()) return ST_InvalidURL;

    // 连接服务器
    struct sockaddr_in serv_addr;
    std::memset(&serv_addr, 0, sizeof(serv_addr));
    serv_addr.sin_family = AF_INET;
    serv_addr.sin_port = htons(port);
    if (inet_pton(AF_INET, ip.c_str(), &serv_addr.sin_addr) <= 0) {
        std::cerr << "Invalid address." << std::endl;
        return ST_InvalidURL;
    }

#ifdef _WIN32
    unsigned long flag = 0;
    // 设置非阻塞
    flag = 1;
    if (ioctlsocket(m_iSocketFd, FIONBIO, &flag) != 0) return ST_IOOperationErr;

    // 连接服务器
    if (connect(m_iSocketFd, (struct sockaddr*)&serv_addr, sizeof(serv_addr)) < 0) {
        // 使用select等待连接
        struct timeval tv;
        tv.tv_sec = 1;
        tv.tv_usec = 0;

        fd_set writefds;
        FD_ZERO(&writefds);
        FD_SET(m_iSocketFd, &writefds);

        int error = 0;
        int len = sizeof(error);

        if (select(m_iSocketFd + 1, NULL, &writefds, NULL, &tv) > 0 && FD_ISSET(m_iSocketFd, &writefds) && (getsockopt(m_iSocketFd, SOL_SOCKET, SO_ERROR, (char*)&error, (socklen_t*)&len), error == 0)) {
        }
        else {
            return ST_ConnectErr;
        }
    }
    // 设置阻塞
    // flag = 0;
    // if (ioctlsocket(m_iSocketFd, FIONBIO, &flag) != 0) return ST_IOOperationErr;
#else
    // 设置非阻塞
    int flags = fcntl(m_iSocketFd, F_GETFL, 0);
    if (flags == -1) return ST_IOOperationErr;

    flags |= O_NONBLOCK;
    if (fcntl(m_iSocketFd, F_SETFL, flags) == -1) return ST_IOOperationErr;

    // 连接服务器
    if (connect(m_iSocketFd, (struct sockaddr*)&serv_addr, sizeof(serv_addr)) < 0) {
        // 使用select等待连接
        struct timeval tv;
        tv.tv_sec = 1;
        tv.tv_usec = 0;

        fd_set writefds;
        FD_ZERO(&writefds);
        FD_SET(m_iSocketFd, &writefds);

        int error = 0;
        int len = sizeof(error);

        if (select(m_iSocketFd + 1, NULL, &writefds, NULL, &tv) > 0 && FD_ISSET(m_iSocketFd, &writefds) && (getsockopt(m_iSocketFd, SOL_SOCKET, SO_ERROR, &error, (socklen_t*)&len), error == 0)) {
        }
        else {
            return ST_ConnectErr;
        }
    }

    // 设置阻塞
    flags = fcntl(m_iSocketFd, F_GETFL, 0);
    if (flags == -1) return ST_IOOperationErr;
    flags &= ~O_NONBLOCK;
    if (fcntl(m_iSocketFd, F_SETFL, flags) == -1) return ST_IOOperationErr;
#endif

    return ST_NoErr;
}

uint64_t RTSPClient::ntohll_custom(uint64_t val){
    const uint32_t i = 1;
    const bool is_little_endian = (*(char*)&i == 1);
    if (is_little_endian) {
        uint64_t res = 0; char* p_val = (char*)&val; char* p_res = (char*)&res;
        for (int k = 0; k < 8; ++k) p_res[k] = p_val[7 - k];
        return res;
    }
    return val;
}

int RTSPClient::closeSocket()
{
    if (m_iSocketFd > 0)
    {
        // 关闭socket
#ifdef _WIN32
        closesocket(m_iSocketFd);
        //WSACleanup();
#else
        close(m_iSocketFd);
#endif
        m_iSocketFd = -1;
    }
    return 0;
}

















