﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="源文件">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;c++;cppm;ixx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="头文件">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;h++;hm;inl;inc;ipp;xsd</Extensions>
    </Filter>
    <Filter Include="资源文件">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="include">
      <UniqueIdentifier>{ec60d373-f152-4b2b-a0ac-d62253db1fda}</UniqueIdentifier>
    </Filter>
    <Filter Include="src">
      <UniqueIdentifier>{44eeb9a4-90e6-475a-b2ad-86aa0531b995}</UniqueIdentifier>
    </Filter>
    <Filter Include="example">
      <UniqueIdentifier>{897ae8ba-fd4f-4be6-926f-d34039e1d68f}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\internal">
      <UniqueIdentifier>{afdad67f-5690-455b-b1e5-d76154bafe86}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\products">
      <UniqueIdentifier>{642861f9-a4d7-495f-86c9-a77e971f56cc}</UniqueIdentifier>
    </Filter>
    <Filter Include="src\Factories">
      <UniqueIdentifier>{f4526e7e-fba3-4e23-9c80-3a979e9df769}</UniqueIdentifier>
    </Filter>
    <Filter Include="UI">
      <UniqueIdentifier>{6d40989f-03da-47c6-9539-d8901d764b65}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\factories\RtspSoftwareFactory.cpp">
      <Filter>src\Factories</Filter>
    </ClCompile>
    <ClCompile Include="src\products\OpenGLConsumer.cpp">
      <Filter>src\products</Filter>
    </ClCompile>
    <ClCompile Include="src\products\RTSP.cpp">
      <Filter>src\products</Filter>
    </ClCompile>
    <ClCompile Include="src\products\SoftDecoder.cpp">
      <Filter>src\products</Filter>
    </ClCompile>
    <ClCompile Include="example\main.cpp">
      <Filter>example</Filter>
    </ClCompile>
    <ClCompile Include="src\MediaPipeline.cpp">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\MediaPlayerFacade.cpp">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="src\MyPlayer.cpp">
      <Filter>src</Filter>
    </ClCompile>
    <ClCompile Include="include\MyMainWindow.cpp">
      <Filter>源文件</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="src\factories\RtspSoftwareFactory.h">
      <Filter>src\Factories</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\EventDispatcher.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\IDataConsumer.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\IMediaFactory.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\IMediaPipeline.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\IProtocolHandler.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\IVideoDecoder.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\MediaPlayerTypes.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\Share.h">
      <Filter>src\internal</Filter>
    </ClInclude>
    <ClInclude Include="src\products\OpenGLConsumer.h">
      <Filter>src\products</Filter>
    </ClInclude>
    <ClInclude Include="src\products\RTSP.h">
      <Filter>src\products</Filter>
    </ClInclude>
    <ClInclude Include="src\products\SoftDecoder.h">
      <Filter>src\products</Filter>
    </ClInclude>
    <ClInclude Include="include\MediaPlayer.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\MediaPlayerFacade.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="include\MediaPipeline.h">
      <Filter>include</Filter>
    </ClInclude>
    <ClInclude Include="src\internal\PrivateQueue.hpp">
      <Filter>src\internal</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <QtUic Include="MyMainWindow.ui">
      <Filter>UI</Filter>
    </QtUic>
  </ItemGroup>
  <ItemGroup>
    <QtMoc Include="include\MyMainWindow.h">
      <Filter>头文件</Filter>
    </QtMoc>
  </ItemGroup>
</Project>