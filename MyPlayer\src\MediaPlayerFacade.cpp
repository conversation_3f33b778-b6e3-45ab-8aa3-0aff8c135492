#include "MediaPlayerFacade.h"
#include "MediaPipeline.h"
#include "factories/RtspSoftwareFactory.h"
#include "IDataConsumer.h"
MediaPlayerFacade::MediaPlayerFacade(const Properties& config){
    m_dispatcher = std::make_shared<EventDispatcher>();
    auto proto_typr = std::any_cast<ProtocolType>(config.at("protocol_type"));
    auto deco_type = std::any_cast<DecoderType>(config.at("decoder_type"));

    if(proto_typr == ProtocolType::RTSP){
        if(deco_type == DecoderType::Software){
            m_factory = std::make_unique<RTSPSoftwareFactory>();
        }else{
            m_factory = std::make_unique<RTSPSoftwareFactory>();
        }
    }else if(proto_typr == ProtocolType::Private){
        if(deco_type == DecoderType::Hardware){
            m_factory = std::make_unique<RTSPSoftwareFactory>();
        }else{
            m_factory = std::make_unique<RTSPSoftwareFactory>();
        }
    }else{
        throw std::runtime_error("Unsupported media type");
    }
    auto client = m_factory->createStreamClient(config);
    auto decoder = m_factory->createVideoDecoder(config);
    
    m_pipeline = std::make_unique<MediaPipelineImpl>(std::move(client), std::move(decoder),m_dispatcher);
    if (config.count("render_enabled") && std::any_cast<bool>(config.at("render_enabled"))) {
        m_consumers.push_back(m_factory->createDataConsumer("render", config, m_dispatcher));
    }

    for (const auto& consumer : m_consumers) {
        if (!consumer || !consumer->initialize(config)) throw std::runtime_error("һ������ģ���ʼ��ʧ�ܡ�");
    }
    //m_pipeline->setOutputCallback([this](DecodedFrame* frame) {
    //    if (frame) {
    //        for (const auto& consumer : m_consumers) {
    //            consumer->postFrame(*frame);
    //        }
    //    }
    //    });
}

MediaPlayerFacade::~MediaPlayerFacade() {
    if (isRunning()) {
        stop();
    }
}

bool MediaPlayerFacade::start() {
    for (const auto& consumer : m_consumers) {
        if (consumer->start())
            return false;
    }
    return m_pipeline->start();
}

void MediaPlayerFacade::stop() {
    m_pipeline->stop();
    for (const auto& consumer : m_consumers) {
        consumer->stop();
    }
}

bool MediaPlayerFacade::isRunning() const{
    return m_pipeline && m_pipeline->isRunning();
}

