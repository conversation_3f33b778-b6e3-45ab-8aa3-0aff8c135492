#pragma once
#include <iostream>
#include <thread>
#include <deque>
#include <regex>
#include <mutex>
#include <condition_variable>

#include "IProtocolHandler.h"
#include "share.h"
#include "internal/EventDispatcher.h"
#ifdef _WIN32
#include <WinSock2.h>
#pragma comment(lib, "ws2_32.lib")
#include <WS2tcpip.h>
#else
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#endif

enum class ParserState {
	Synchronizing,
	ReadingHead,
	ReadingBody
};

class RTSPClient :public IProtocolHandler {
public:
	RTSPClient(const Properties pProperties,std::shared_ptr<EventDispatcher> dispatcher);
	~RTSPClient();
	bool initialize() override;
	int OpenStream() override;
	int CloseStream()override;
	int getIdentity() override;
	//void SetCallback(STRTSPSourceCallBack cb) override;
	void SetCallback(FrameCallback cb) override;
	bool updateProperties(Properties* _properties) override;
private:
	int createSocket();
	int connectToServer();
	int sendCommand(const void* command, const int size);
	uint64_t ntohll_custom(uint64_t);
	void keepAliverLoop();
	void receiverLoop();
	void parserLoop();
	int closeSocket();
	template<typename T>
	void get_property_optional(const std::string& key, T& value, const T& default_value);
	template<typename U>
	bool try_get_property(const std::string& key, U& out_value);
private:
	std::shared_ptr<EventDispatcher> m_dispatcher;
	const Properties m_Properties;
	bool m_bStreamOverTCP;
	int m_iIdentity;
	SockType m_iSocketFd;
	void* m_pUserPtr;
	int m_iUserChannelId;
	FrameCallback m_framecallback;
	//STRTSPSourceCallBack m_rtspsourcecallback;

	std::deque<ParsedPacket> m_packetQueue;
	// std::vector<ParsedPacket> m_packetQueue;
	std::mutex m_packetQueueMutex;
	std::condition_variable m_packetQueueCond;

	std::deque<unsigned char> m_receiveBuffer;
	std::mutex m_receiveBuffmutex;
	std::condition_variable m_receiveBuffCond;

	std::thread m_reciverThread;
	std::thread m_parserThread;
	std::thread keepAliverThread;

	std::mutex m_mutex;
	std::string m_strUrl;
	ParserState m_parserState;
	size_t m_expectedPacketSize;

	std::mutex m_keepAliveMutex;
	std::condition_variable m_keepAliveCondition;
	std::vector<char> m_buffer;
	std::atomic<bool> m_running;
};