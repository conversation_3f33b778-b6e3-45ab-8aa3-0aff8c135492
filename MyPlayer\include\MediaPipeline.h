#include"internal/IMediaPipeline.h"
#include"internal/IProtocolHandler.h"
#include "internal/IVideoDecoder.h"
#include "internal/EventDispatcher.h"
class MediaPipelineImpl :public IMediaPipeline {
public:
	MediaPipelineImpl(std::unique_ptr<IProtocolHandler> client,
		std::unique_ptr<IVideoDecoder> decoder,
		std::shared_ptr<EventDispatcher> dispatcher);
	//MediaPipelineImpl(std::unique_ptr<IProtocolHandler> client, std::unique_ptr<IVideoDecoder>decoder);
	~MediaPipelineImpl()override;
	//void setOutputCallback(std::function<void(DecodedFrame*)> cb) override;
	bool start()override;
	void stop()override;
	bool isRunning() const override;
private:
	//static int dataCallback(int, void*, int, const void*, void* );
	void processData(int flags, ST_FRAME* frame);
	std::unique_ptr<IProtocolHandler> m_client;
	std::unique_ptr<IVideoDecoder> m_decoder;
	std::shared_ptr<EventDispatcher> m_dispatcher;
	std::atomic<bool> m_isRunning{ false };
};